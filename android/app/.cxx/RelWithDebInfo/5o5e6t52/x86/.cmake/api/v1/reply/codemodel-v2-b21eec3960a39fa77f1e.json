{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/project/find_word/android/app/.cxx/RelWithDebInfo/5o5e6t52/x86", "source": "/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 1}}