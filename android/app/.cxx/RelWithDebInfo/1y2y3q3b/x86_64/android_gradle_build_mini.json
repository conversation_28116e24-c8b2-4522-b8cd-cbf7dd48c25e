{"buildFiles": ["/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/project/find_word/android/app/.cxx/RelWithDebInfo/1y2y3q3b/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/project/find_word/android/app/.cxx/RelWithDebInfo/1y2y3q3b/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}